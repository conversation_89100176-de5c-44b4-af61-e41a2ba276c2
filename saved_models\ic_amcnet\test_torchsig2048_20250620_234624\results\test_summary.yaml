dataset_info:
  dataset_type: torchsig2048
  input_shape: !!python/tuple
  - 2
  - 2048
  num_classes: 25
  snr_range:
  - 0.0
  - 30.0
  total_samples: 208000
inference_performance:
  avg_inference_time_ms: 0.026894889198816743
  max_inference_time_ms: 0.6297044456005096
  min_inference_time_ms: 0.008001923561096191
  std_inference_time_ms: 0.015214229115280425
model_complexity:
  macs: 187.060M
  macs_raw: 187060352.0
  parameters: 16.912M
  params_raw: 16912409.0
overall_metrics:
  accuracy: 56.81826923076923
  kappa: 0.5501903044871794
  macro_f1: 54.68897661115613
test_info:
  config_path: config.yaml
  model_path: ./saved_models/ic_amcnet/torchsig2048_20250619_033139/models/best_model.pth
  test_date: '2025-06-20 23:48:01'
