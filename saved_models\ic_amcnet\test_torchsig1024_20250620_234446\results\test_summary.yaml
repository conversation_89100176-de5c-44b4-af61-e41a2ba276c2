dataset_info:
  dataset_type: torchsig1024
  input_shape: !!python/tuple
  - 2
  - 1024
  num_classes: 25
  snr_range:
  - 0.0
  - 30.0
  total_samples: 208000
inference_performance:
  avg_inference_time_ms: 0.025854357160054717
  max_inference_time_ms: 0.5072467029094696
  min_inference_time_ms: 0.007793307304382324
  std_inference_time_ms: 0.01338664450748248
model_complexity:
  macs: 93.737M
  macs_raw: 93737088.0
  parameters: 8.524M
  params_raw: 8523801.0
overall_metrics:
  accuracy: 55.53317307692308
  kappa: 0.5368038862179487
  macro_f1: 52.91508613539872
test_info:
  config_path: config.yaml
  model_path: ./saved_models/ic_amcnet/torchsig1024_20250619_031704/models/best_model.pth
  test_date: '2025-06-20 23:45:58'
